@php
    // No prefix needed anymore
    $prefix = '';

    // Check if current URL is in settings section
    $isSettingsSection = request()->is('settings/*');
    // Get the specific settings page (user or variable)
    $currentSettingsPage = request()->segment(2);
@endphp

<nav class="p-4">
    <div class="space-y-2">
        <a href="{{ $prefix }}/panel"
           class="flex items-center px-4 py-2 rounded-lg hover:bg-gray-100 {{ request()->is('panel') ? 'bg-gray-100' : '' }}">
            <x-icon name="home" width="16" height="16" class="mr-3" />
            Panel
        </a>

        @if(auth()->user()->isAdmin() || auth()->user()->isStaff())
        <a href="{{ $prefix }}/donasi"
           class="flex items-center px-4 py-2 rounded-lg hover:bg-gray-100 {{ request()->is('donasi') ? 'bg-gray-100' : '' }}">
            <x-icon name="heart-handshake" width="16" height="16" class="mr-3" />
            Donasi
        </a>
        @endif

        <a href="{{ $prefix }}/program"
           class="flex items-center px-4 py-2 rounded-lg hover:bg-gray-100 {{ request()->is('program') || request()->is('program/*') ? 'bg-gray-100' : '' }}">
            <x-icon name="program" width="16" height="16" class="mr-3" />
            Program
        </a>

        @if(auth()->user()->isAdmin() || auth()->user()->isStaff())
        <a href="{{ $prefix }}/donatur"
           class="flex items-center px-4 py-2 rounded-lg hover:bg-gray-100 {{ request()->is('donatur') || request()->is('donatur/*') ? 'bg-gray-100' : '' }}">
            <x-icon name="donatur" width="16" height="16" class="mr-3" />
            Donatur
        </a>
        @endif

        @if(auth()->user()->isAdmin() || auth()->user()->isStaff())
        <a href="{{ $prefix }}/relawan"
           class="flex items-center px-4 py-2 rounded-lg hover:bg-gray-100 {{ request()->is('relawan') || request()->is('relawan/*') ? 'bg-gray-100' : '' }}">
            <x-icon name="users" width="16" height="16" class="mr-3" />
            Relawan
        </a>
        @endif

        @if(auth()->user()->isAdmin())
        <!-- Settings Dropdown -->
        <div class="relative" x-data="{ open: {{ $isSettingsSection ? 'true' : 'false' }} }">
            <button @click="open = !open"
                    class="w-full flex items-center px-4 py-2 text-left rounded-lg hover:bg-gray-100"
                    :class="{ 'bg-gray-100': open || {{ $isSettingsSection ? 'true' : 'false' }} }">
                <x-icon name="settings" width="16" height="16" class="mr-3" />
                <span>Setting</span>
                <span class="ml-auto transform transition-transform duration-200" :class="{ 'rotate-180': open }">
                    <x-icon name="chevron-down" width="16" height="16" />
                </span>
            </button>
            <div x-show="open"
                 x-transition:enter="transition ease-out duration-200"
                 x-transition:enter-start="transform opacity-0 scale-95"
                 x-transition:enter-end="transform opacity-100 scale-100"
                 x-transition:leave="transition ease-in duration-100"
                 x-transition:leave-start="transform opacity-100 scale-100"
                 x-transition:leave-end="transform opacity-0 scale-95"
                 class="pl-4 mt-2 space-y-2">
                <a href="{{ $prefix }}/settings/user"
                   class="flex items-center px-4 py-2 rounded-lg hover:bg-gray-100 transition-colors duration-200 {{ $currentSettingsPage === 'user' ? 'bg-gray-100' : '' }}">
                    <x-icon name="user" width="16" height="16" class="mr-3" />
                    User
                </a>

                <a href="{{ $prefix }}/settings/variables"
                   class="flex items-center px-4 py-2 rounded-lg hover:bg-gray-100 transition-colors duration-200 {{ $currentSettingsPage === 'variable' ? 'bg-gray-100' : '' }}">
                    <x-icon name="database" width="16" height="16" class="mr-3" />
                    Variables
                </a>
            </div>
        </div>
        @endif
    </div>
</nav>
