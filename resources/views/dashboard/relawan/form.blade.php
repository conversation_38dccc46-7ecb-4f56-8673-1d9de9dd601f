<x-modal-dialog :show-variable="'showDialog'" :max-width="'lg'" :close-method="'closeDialog()'">
    <div class="flex justify-between items-center mb-4">
        <h3 class="text-xl font-semibold text-gray-900" x-text="dialogMode === 'create' ? 'Add New Relawan' : 'Edit Relawan'"></h3>
        <button @click="closeDialog()" class="text-gray-400 hover:text-gray-500 focus:outline-none">
            <x-icon name="x" width="24" height="24" />
        </button>
    </div>

    <form @submit.prevent="saveRelawan">
        <div class="space-y-4">
            <div>
                <label for="name" class="block text-sm text-gray-500 mb-1">Name <span class="text-red-500">*</span></label>
                <input 
                    type="text" 
                    id="name" 
                    x-model="relawan.name" 
                    class="block w-full px-3 py-2 text-sm rounded-md border border-gray-300 shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-30 transition duration-150"
                    x-bind:class="{'border-red-300 focus:ring-red-500': errors.name}"
                >
                <div x-show="errors.name" x-text="errors.name" class="mt-1 text-sm text-red-500"></div>
            </div>
            
            <div>
                <label for="relawan-email" class="block text-sm text-gray-500 mb-1">Email</label>
                <input 
                    type="email"
                    id="relawan-email"
                    x-model="relawan.email"
                    class="block w-full px-3 py-2 text-sm rounded-md border border-gray-300 shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-30 transition duration-150"
                    x-bind:class="{'border-red-300 focus:ring-red-500': errors.email}"
                >
                <div x-show="errors.email" x-text="errors.email" class="mt-1 text-sm text-red-500"></div>
            </div>
            
            <div>
                <label for="phone" class="block text-sm text-gray-500 mb-1">Phone</label>
                <x-phone-input
                    id="phone"
                    name="phone"
                    x-model="relawan.phone"
                    placeholder="+62 8xx xxxx xxxx"
                    class="block w-full px-3 py-2 text-sm rounded-md border border-gray-300 shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-30 transition duration-150"
                    x-bind:class="{'border-red-300 focus:ring-red-500': errors.phone}"
                />
                <div x-show="errors.phone" x-text="errors.phone" class="mt-1 text-sm text-red-500"></div>
            </div>

            <div>
                <label for="relawan-address" class="block text-sm text-gray-500 mb-1">Address</label>
                <textarea 
                    id="relawan-address"
                    x-model="relawan.address"
                    rows="3"
                    class="block w-full px-3 py-2 text-sm rounded-md border border-gray-300 shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-30 transition duration-150"
                    x-bind:class="{'border-red-300 focus:ring-red-500': errors.address}"
                ></textarea>
                <div x-show="errors.address" x-text="errors.address" class="mt-1 text-sm text-red-500"></div>
            </div>

            <div>
                <label for="relawan-description" class="block text-sm text-gray-500 mb-1">Description</label>
                <textarea 
                    id="relawan-description"
                    x-model="relawan.description"
                    rows="3"
                    class="block w-full px-3 py-2 text-sm rounded-md border border-gray-300 shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-30 transition duration-150"
                    x-bind:class="{'border-red-300 focus:ring-red-500': errors.description}"
                ></textarea>
                <div x-show="errors.description" x-text="errors.description" class="mt-1 text-sm text-red-500"></div>
            </div>

            <div>
                <label for="program-form" class="block text-sm text-gray-500 mb-1">Programs</label>
                <div class="space-y-2">
                    <template x-for="(program, index) in relawan.programs" :key="program.id">
                        <div class="flex items-center justify-between px-3 py-2 text-sm rounded-md border border-gray-300 bg-gray-50">
                            <span x-text="program.name"></span>
                            <button type="button" @click="removeProgram(index)" class="text-gray-400 hover:text-gray-600">
                                <x-icon name="x" width="16" height="16" />
                            </button>
                        </div>
                    </template>
                    
                    <div class="relative">
                        <input 
                            type="text" 
                            id="program-search"
                            x-model="programSearchTerm"
                            @input="searchPrograms()"
                            @focus="if(programSearchTerm.length >= 2) searchPrograms()"
                            placeholder="Search for programs..."
                            class="block w-full px-3 py-2 text-sm rounded-md border border-gray-300 shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-30 transition duration-150"
                        >
                        
                        <!-- Loading indicator -->
                        <div x-show="programLoading" class="absolute right-3 top-2.5">
                            <x-icon name="spinner" width="16" height="16" class="animate-spin text-gray-400" />
                        </div>
                        
                        <!-- Results dropdown -->
                        <div 
                            x-show="showProgramResults" 
                            class="absolute z-10 w-full mt-1 bg-white border border-gray-300 rounded-md shadow-lg max-h-60 overflow-y-auto"
                        >
                            <div class="py-1">
                                <template x-for="program in filteredPrograms" :key="program.id">
                                    <button
                                        type="button"
                                        @click="addProgram(program)"
                                        class="w-full px-4 py-2 text-sm text-left text-gray-700 hover:bg-gray-100 focus:outline-none focus:bg-gray-100"
                                    >
                                        <span x-text="program.name" class="truncate"></span>
                                    </button>
                                </template>
                            </div>
                        </div>
                    </div>
                </div>
                <div x-show="errors.program_ids" x-text="errors.program_ids" class="mt-1 text-sm text-red-500"></div>
            </div>

            {{-- Social Media Links --}}
            <div>
                <label class="block text-sm text-gray-500 mb-1">Social Media Links</label>
                <div class="space-y-2">
                    <template x-for="(link, index) in relawan.social_media" :key="index">
                        <div class="flex items-center space-x-2">
                            <input 
                                type="text" 
                                x-model="relawan.social_media[index]" 
                                placeholder="https://example.com/profile"
                                class="block flex-1 px-3 py-2 text-sm rounded-md border border-gray-300 shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-30 transition duration-150"
                            >
                            <button type="button" @click="removeSocialMediaInput(index)" class="p-2 text-gray-400 hover:text-gray-600">
                                <x-icon name="trash" width="16" height="16" />
                            </button>
                        </div>
                    </template>
                    
                    <button type="button" @click="addSocialMediaInput()" class="inline-flex items-center px-3 py-1.5 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                        <x-icon name="plus" width="16" height="16" class="mr-2" />
                        Add Social Media Link
                    </button>
                </div>
                <div x-show="errors.social_media" x-text="errors.social_media" class="mt-1 text-sm text-red-500"></div>
            </div>

            <!-- File upload component -->
            <x-file-upload
                id="image"
                label="Image"
                :image-url-var="'relawan.image_url'"
                :image-path-var="'relawan.image_path'"
                :selected-file-var="'selectedFile'"
                :errors-var="'errors'"
                alt-text="Relawan Image"
                :max-size="2"
            />
        </div>
        <div class="flex justify-end space-x-3 pt-4 border-t border-gray-100 mt-4">
            <button type="button" @click="saveRelawan()" class="flex px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 shadow-sm transition duration-150" x-bind:disabled="loading">
                <span x-show="!loading">Save</span>
                <span x-show="loading">
                    <x-icon name="spinner" width="16" height="16" class="animate-spin text-white my-1" />
                </span>
            </button>
        </div>
    </form>
</x-modal-dialog>
