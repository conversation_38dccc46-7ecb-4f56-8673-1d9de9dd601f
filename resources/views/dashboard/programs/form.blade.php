<x-modal-dialog :show-variable="'showDialog'" :max-width="'lg'" :close-method="'closeDialog()'">
    <div class="flex justify-between items-center mb-4">
        <h3 class="text-xl font-semibold text-gray-900" x-text="dialogTitle"></h3>
        <button @click="closeDialog()" class="text-gray-400 hover:text-gray-500 focus:outline-none">
            <x-icon name="x" width="24" height="24" />
        </button>
    </div>

    <!-- Image preview at top -->
    <div x-show="formData.image_url && !selectedFile" class="mb-4">
        <img :src="formData.image_url" class="w-full max-h-48 object-contain rounded-md bg-gray-50" alt="Program Banner">
    </div>

    <!-- Image preview for newly selected file -->
    <div x-show="selectedFile" class="mb-4">
        <img x-ref="imagePreview" class="w-full max-h-48 object-contain rounded-md bg-gray-50" alt="Selected Image">
    </div>

    <form @submit.prevent="submitForm()">
        <div class="space-y-4">
            <!-- Name field -->
            <div>
                <label for="name" class="block text-sm text-gray-500 mb-1">Name <span class="text-red-500">*</span></label>
                <input 
                    type="text"
                    id="name"
                    x-model="formData.name"
                    class="block w-full px-3 py-2 text-sm rounded-md border border-gray-300 shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-30 transition duration-150"
                    x-bind:class="{'border-red-300 focus:ring-red-500': errors.name}"
                >
                <div x-show="errors.name" x-text="errors.name" class="mt-1 text-sm text-red-500"></div>
            </div>
            
            <!-- Description field -->
            <div>
                <label for="description" class="block text-sm text-gray-500 mb-1">Description</label>
                <textarea 
                    id="description"
                    x-model="formData.description"
                    rows="4"
                    class="block w-full px-3 py-2 text-sm rounded-md border border-gray-300 shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-30 transition duration-150"
                    x-bind:class="{'border-red-300 focus:ring-red-500': errors.description}"
                ></textarea>
                <div x-show="errors.description" x-text="errors.description" class="mt-1 text-sm text-red-500"></div>
            </div>
            
            <!-- Transaction Code field -->
            <div>
                <div class="flex justify-between items-center mb-1">
                    <label for="transaction_code" class="block text-sm text-gray-500">Kode Unik (0-999) <span class="text-red-500">*</span></label>
                    <button
                        type="button"
                        @click="generateUniqueCode()"
                        class="flex items-center text-blue-600 hover:text-blue-700 text-sm transition duration-150"
                        title="Generate unique code"
                        x-bind:disabled="generatingCode"
                    >
                        <x-icon name="refresh-cw" width="12" height="12" x-bind:class="{'animate-spin': generatingCode}" />
                        <span class="ml-1 text-xs" x-text="generatingCode ? 'Generating...' : 'Generate'"></span>
                    </button>
                </div>
                <input
                    type="number"
                    id="transaction_code"
                    x-model="formData.transaction_code"
                    class="block w-full px-3 py-2 text-sm rounded-md border border-gray-300 shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-30 transition duration-150"
                    x-bind:class="{'border-red-300 focus:ring-red-500': errors.transaction_code}"
                    @keydown="validateNumberInput"
                    min="0"
                    max="999"
                    required
                >
                <div x-show="errors.transaction_code" x-text="errors.transaction_code" class="mt-1 text-sm text-red-500"></div>
            </div>
            
            <!-- Target field -->
            <div>
                <label for="target" class="block text-sm text-gray-500 mb-1">Target (IDR)</label>
                <input 
                    type="text"
                    id="target"
                    x-model="formData.target"
                    class="block w-full px-3 py-2 text-sm rounded-md border border-gray-300 shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-30 transition duration-150"
                    x-bind:class="{'border-red-300 focus:ring-red-500': errors.target}"
                    @input="formatCurrency($event.target, 'target')"
                >
                <div x-show="errors.target" x-text="errors.target" class="mt-1 text-sm text-red-500"></div>
            </div>
            
            <!-- End Date field -->
            <div>
                <label for="end_at" class="block text-sm text-gray-500 mb-1">End Date</label>
                <input 
                    type="date"
                    id="end_at"
                    x-model="formData.end_at"
                    class="block w-full px-3 py-2 text-sm rounded-md border border-gray-300 shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-30 transition duration-150"
                    x-bind:class="{'border-red-300 focus:ring-red-500': errors.end_at}"
                >
                <div x-show="errors.end_at" x-text="errors.end_at" class="mt-1 text-sm text-red-500"></div>
            </div>
            
            <!-- Status field -->
            <div>
                <label for="status" class="block text-sm text-gray-500 mb-1">Status <span class="text-red-500">*</span></label>
                <div class="relative">
                    <select 
                        id="status"
                        x-model="formData.status"
                        class="block w-full px-3 py-2 text-sm rounded-md border border-gray-300 shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-30 transition duration-150 appearance-none"
                        :class="errors.role ? 'border-red-300 focus:ring-red-500' : ''"
                    >
                        <option value="draft">Draft</option>
                        <option value="ongoing">Ongoing</option>
                        <option value="done">Done</option>
                        <option value="archive">Archive</option>
                    </select>
                    <div class="pointer-events-none absolute inset-y-0 right-0 flex items-center px-2 text-gray-500">
                        <x-icon name="chevron-down" width="16" height="16" />
                    </div>
                </div>
                <div x-show="errors.status" x-text="errors.status" class="mt-1 text-sm text-red-500"></div>
            </div>

            <!-- File upload input only -->
            <div>
                <label for="image" class="block text-sm text-gray-500 mb-1">Banner Image</label>
                <input
                    type="file"
                    id="image"
                    x-ref="fileInput"
                    @change="
                        selectedFile = $event.target.files[0];
                        errors.image = null;

                        // Validate file size
                        if (selectedFile && selectedFile.size > 2 * 1024 * 1024) {
                            errors.image = 'File size must be less than 2MB';
                            $event.target.value = '';
                            selectedFile = null;
                            return;
                        }

                        // Show image preview
                        if (selectedFile) {
                            const reader = new FileReader();
                            reader.onload = (e) => {
                                $refs.imagePreview.src = e.target.result;
                            };
                            reader.readAsDataURL(selectedFile);
                        }
                    "
                    accept="image/*"
                    class="block w-full px-3 py-2 text-sm rounded-md border border-gray-300 shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-30 transition duration-150"
                    x-bind:class="{'border-red-300 focus:ring-red-500': errors.image}"
                >
                <div x-show="errors.image" x-text="errors.image" class="mt-1 text-sm text-red-500"></div>
            </div>
        </div>
        <div class="flex justify-end space-x-3 pt-4 border-t border-gray-100 mt-4">
            <button type="button" @click="submitForm()" class="flex px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 shadow-sm transition duration-150" x-bind:disabled="loading">
                <span x-show="!loading">Save</span>
                <span x-show="loading">
                    <x-icon name="spinner" width="16" height="16" class="animate-spin text-white my-1" />
                </span>
            </button>
        </div>
    </form>
</x-modal-dialog>
