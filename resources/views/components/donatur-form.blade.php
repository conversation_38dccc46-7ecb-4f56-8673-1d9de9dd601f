@props(['initialValue' => null, 'placeholder' => 'Pilih atau buat Donatur', 'id' => 'donatur-form-input'])

<div x-data="donaturForm({{ json_encode($initialValue) }})" x-init="window._donaturFormId = $el.getAttribute('id') || '{{ $id }}'" id="{{ $id }}" {{ $attributes->merge(['class' => 'relative']) }}>
    <!-- Removed redundant Donatur label -->
    <div class="relative h-full">
        <input type="text" id="{{ $id }}" x-model="searchTerm" @input.debounce.400ms="searchDonaturs()" placeholder="Donatur" class="block w-full h-[38px] px-3 py-2 text-sm rounded-md border border-gray-300 shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-30 transition duration-150" x-show="!selectedDonatur || !selectedDonatur.name" />
        <div x-show="showResults" class="absolute z-10 w-full mt-1 bg-white border border-gray-300 rounded-md shadow-lg max-h-60 overflow-y-auto">
            <template x-for="donatur in filteredDonaturs" :key="donatur.id">
                <button type="button" @click="selectDonatur(donatur)" class="w-full px-4 py-2 text-sm text-left text-gray-700 hover:bg-gray-100">
                    <span x-text="donatur.name" class="truncate"></span>
                </button>
            </template>
            <template x-if="searchTerm && filteredDonaturs.length === 0">
                <button type="button" @click="selectNewDonatur(searchTerm)" class="w-full px-4 py-2 text-sm text-left text-blue-700 hover:bg-blue-50">
                    Tambah Donatur Baru: <span x-text="searchTerm" class="truncate"></span>
                </button>
            </template>
        </div>
        <div x-show="selectedDonatur && selectedDonatur.name" class="flex items-center justify-between w-full h-[38px] px-3 py-2 text-sm rounded-md border border-gray-300 bg-gray-50">
            <div class="truncate flex-1 mr-1">
                <span x-text="selectedDonatur ? selectedDonatur.name : ''" class="truncate"></span>
                <span x-show="selectedDonatur && selectedDonatur.manager" class="text-xs text-gray-500 ml-1" x-text="selectedDonatur && selectedDonatur.manager ? '(' + selectedDonatur.manager + ')' : ''"></span>
            </div>
            <button type="button" @click="clearSelection()" class="text-gray-400 hover:text-gray-600 flex-shrink-0">
                <x-icon name="x" width="16" height="16" />
            </button>
        </div>
    </div>
</div>

<script>
document.addEventListener('alpine:init', () => {
    Alpine.data('donaturForm', (initialValue) => ({
        mode: 'select',
        searchTerm: '',
        showResults: false,
        filteredDonaturs: [],
        selectedDonatur: initialValue,
        newDonaturName: '',
        createdDonatur: false,

        init() {
            // Listen for set-selected-donatur event
            this.$el.addEventListener('set-selected-donatur', (event) => {
                if (event.detail && event.detail.donatur) {
                    this.selectedDonatur = event.detail.donatur;
                    this.$dispatch('input', event.detail.donatur);
                }
            });

            // Listen for reset event
            this.$el.addEventListener('reset-form', () => {
                this.selectedDonatur = { name: '' };
                this.searchTerm = '';
                this.showResults = false;
                this.filteredDonaturs = [];
            });
        },
        searchDonaturs() {
            if (this.searchTerm.length < 2) {
                this.showResults = false;
                return;
            }
            // Fetch donaturs from API
            fetch(`/donatur/search?q=${this.searchTerm}`)
                .then(res => res.json())
                .then(data => {
                    this.filteredDonaturs = data;
                    this.showResults = true;
                });
        },
        selectDonatur(donatur) {
            this.selectedDonatur = donatur;
            this.showResults = false;
            this.$dispatch('input', donatur);
        },
        selectNewDonatur(name) {
            this.selectedDonatur = { id: null, name };
            this.showResults = false;
            this.$dispatch('input', this.selectedDonatur);
        },
        clearSelection() {
            this.selectedDonatur = { name: '' };
            this.$dispatch('input', { name: '' });
        },

    }));
});
</script>
