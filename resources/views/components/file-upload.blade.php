@props([
    'id' => 'file-upload',
    'label' => 'Image',
    'accept' => 'image/*',
    'maxSize' => 2, // MB
    'imageUrlVar' => 'formData.image_url',
    'imagePathVar' => 'formData.image_path',
    'selectedFileVar' => 'selectedFile',
    'errorsVar' => 'errors',
    'altText' => 'Image Preview',
    'required' => false,
    'showPreview' => true
])

@if($showPreview)
<!-- Image preview (if available) -->
<div x-show="{{ $imageUrlVar }} && !{{ $selectedFileVar }}" class="mb-4 relative">
    <img :src="{{ $imageUrlVar }}" class="w-full max-h-48 object-contain rounded-md bg-gray-50" alt="{{ $altText }}">
    <button @click="{{ $imageUrlVar }} = null; {{ $imagePathVar }} = null"
            class="absolute top-2 right-2 bg-white/80 hover:bg-white p-1 rounded-full text-gray-700 hover:text-gray-900 focus:outline-none shadow-md">
        <x-icon name="x" width="20" height="20" />
    </button>
</div>

<!-- Image preview for newly selected file -->
<div x-show="{{ $selectedFileVar }}" class="mb-4 relative">
    <img x-ref="imagePreview" class="w-full max-h-48 object-contain rounded-md bg-gray-50" alt="Selected Image">
    <button @click="{{ $selectedFileVar }} = null; $refs.fileInput.value = ''"
            class="absolute top-2 right-2 bg-white/80 hover:bg-white p-1 rounded-full text-gray-700 hover:text-gray-900 focus:outline-none shadow-md">
        <x-icon name="x" width="20" height="20" />
    </button>
</div>
@endif

<!-- File input field -->
<div>
    <label for="{{ $id }}" class="block text-sm text-gray-500 mb-1">
        {{ $label }}{{ $required ? ' *' : '' }}
    </label>
        <input
            type="file"
            id="{{ $id }}"
            x-ref="fileInput"
            @change="
                {{ $selectedFileVar }} = $event.target.files[0];
                {{ $errorsVar }}.image = null;

                // Validate file size
                if ({{ $selectedFileVar }} && {{ $selectedFileVar }}.size > {{ $maxSize }} * 1024 * 1024) {
                    {{ $errorsVar }}.image = 'File size must be less than {{ $maxSize }}MB';
                    $event.target.value = '';
                    {{ $selectedFileVar }} = null;
                    return;
                }

                // Show image preview
                if ({{ $selectedFileVar }}) {
                    const reader = new FileReader();
                    reader.onload = (e) => {
                        $refs.imagePreview.src = e.target.result;
                    };
                    reader.readAsDataURL({{ $selectedFileVar }});
                }
            "
            accept="{{ $accept }}"
            {{ $required ? 'required' : '' }}
            class="block w-full px-3 py-2 text-sm rounded-md border border-gray-300 shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-30 transition duration-150"
            x-bind:class="{'border-red-300 focus:ring-red-500': {{ $errorsVar }}.image}"
            {{ $attributes }}
        >
        <div x-show="{{ $errorsVar }}.image" x-text="{{ $errorsVar }}.image" class="mt-1 text-sm text-red-500"></div>
    </div>
</div>


