<?php

namespace App\Models;

use App\Models\Traits\HasOrderedUuid;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;

class Program extends Model
{
    use HasFactory, HasOrderedUuid;

    protected $fillable = [
        'name',
        'description',
        'image_path',
        'transaction_code',
        'target',
        'end_at',
        'achievement',
        'status',
    ];

    protected $casts = [
        'end_at' => 'date',
        'target' => 'integer',
        'achievement' => 'integer',
    ];

    /**
     * Scope a query to only include published programs (ongoing or done).
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopePublished($query)
    {
        return $query->whereIn('status', ['ongoing', 'done']);
    }

    /**
     * Generate a unique transaction code
     *
     * @return int
     */
    public static function generateUniqueTransactionCode(): int
    {
        $code = random_int(0, 999);

        // Check if the code already exists in active programs (not archived)
        // This matches the partial unique index constraint
        while (self::where('transaction_code', $code)->where('status', '!=', 'archive')->exists()) {
            $code = random_int(0, 999);
        }

        return $code;
    }

    /**
     * Get the donations associated with this program.
     */
    public function donations()
    {
        return $this->hasMany(Donation::class);
    }

    /**
     * The volunteers associated with the program.
     */
    public function relawans(): BelongsToMany
    {
        return $this->belongsToMany(Relawan::class, 'program_relawan')
            ->withPivot('notes')
            ->withTimestamps();
    }
}
