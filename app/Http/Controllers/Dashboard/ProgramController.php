<?php

namespace App\Http\Controllers\Dashboard;

use App\Http\Controllers\Controller;
use App\Models\Program;
use App\Rules\UniqueTransactionCodeExceptArchived;
use App\Services\ImageService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use Illuminate\Contracts\View\View as ViewContract;

class ProgramController extends Controller
{
    /**
     * The image service instance.
     */
    protected ImageService $imageService;

    /**
     * Create a new controller instance.
     */
    public function __construct(ImageService $imageService)
    {
        $this->imageService = $imageService;
    }

    /**
     * Display a listing of the programs.
     */
    public function index(): ViewContract|string
    {
        // All authenticated users can view programs
        // Only admins can manage (create/edit/delete) programs
        // Non-admin users can only see ongoing and done programs, not draft or archive programs

        $query = Program::query();

        /** @var \App\Models\User $user */
        $user = Auth::user();
        if (!$user->isAdmin()) {
            // Non-admin users can see ongoing and done programs, but not draft or archive programs
            $query->whereIn('status', ['ongoing', 'done']);
        }

        // Custom ordering: draft first, then ongoing, then done, then archive
        $query->orderByRaw("CASE
            WHEN status = 'draft' THEN 1
            WHEN status = 'ongoing' THEN 2
            WHEN status = 'done' THEN 3
            WHEN status = 'archive' THEN 4
            ELSE 5 END");

        // Apply filters if they exist
        if (request()->has('name') && !empty(request('name'))) {
            $query->where('name', 'ilike', '%' . request('name') . '%');
        }

        if (request()->has('transaction_code')) {
            $query->where('transaction_code', (int)request('transaction_code'));
        }

        if (request()->has('status') && !empty(request('status'))) {
            $query->where('status', request('status'));
        }

        $programs = $query->orderBy('id', 'desc')->paginate(10)->withQueryString();

        if (request()->ajax()) {
            return view('dashboard.programs.table', compact('programs'))->render();
        }

        return view('dashboard.programs.index', compact('programs'));
    }

    /**
     * Store a newly created program in storage.
     */
    public function store(Request $request)
    {
        /** @var \App\Models\User $user */
        $user = Auth::user();
        if (!$user->isAdmin()) {
            abort(403, 'Unauthorized action.');
        }

        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'description' => 'nullable|string',
            'image' => 'nullable|image|max:2048', // 2MB max
            'transaction_code' => ['required', 'integer', 'min:0', 'max:999', new UniqueTransactionCodeExceptArchived()],
            'target' => 'nullable|integer|min:0',
            'end_at' => 'nullable|date',
            'achievement' => 'nullable|integer|min:0',
            'status' => 'required|in:draft,ongoing,done,archive',
        ]);

        // Ensure description is never null
        $validated['description'] = $validated['description'] ?? '';



        // For non-admin users, don't allow setting status to draft or archive
        /** @var \App\Models\User $user */
        $user = Auth::user();
        if (!$user->isAdmin() && ($validated['status'] === 'draft' || $validated['status'] === 'archive')) {
            $validated['status'] = 'ongoing';
        }

        // Handle image upload
        if ($request->hasFile('image')) {
            $validated['image_path'] = $this->imageService->processAndStoreImage(
                $request->file('image'),
                'programs'
            );
        }

        // Remove the image field as it's not in the database
        unset($validated['image']);

        try {
            Program::create($validated);
            return response()->json(['message' => 'Program created successfully']);
        } catch (\Illuminate\Database\QueryException $e) {
            // Log the error for debugging
            Log::error('Database error when creating program: ' . $e->getMessage());

            // Check if it's a unique constraint violation for transaction_code
            if (
                str_contains($e->getMessage(), 'programs_transaction_code_active_unique') ||
                str_contains($e->getMessage(), 'duplicate key value violates unique constraint') ||
                str_contains($e->getMessage(), 'UNIQUE constraint failed')
            ) {
                return response()->json([
                    'errors' => [
                        'transaction_code' => ['The kode unik is not unique.']
                    ]
                ], 422);
            }

            // Re-throw the exception if it's not a constraint violation we can handle
            throw $e;
        } catch (\Exception $e) {
            // Log any other errors
            Log::error('Unexpected error when creating program: ' . $e->getMessage());
            throw $e;
        }
    }

    /**
     * Display the specified program.
     */
    public function show(Program $program)
    {
        // All authenticated users can view program details
        $program->image_url = $program->image_path ? Storage::url($program->image_path) : null;

        return response()->json($program);
    }

    /**
     * Update the specified program in storage.
     */
    public function update(Request $request, Program $program)
    {
        /** @var \App\Models\User $user */
        $user = Auth::user();
        if (!$user->isAdmin()) {
            abort(403, 'Unauthorized action.');
        }

        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'description' => 'nullable|string',
            'image' => 'nullable|image|max:2048', // 2MB max
            'transaction_code' => ['required', 'integer', 'min:0', 'max:999', new UniqueTransactionCodeExceptArchived($program->id)],
            'target' => 'nullable|integer|min:0',
            'end_at' => 'nullable|date',
            'achievement' => 'nullable|integer|min:0',
            'status' => 'required|in:draft,ongoing,done,archive',
        ]);

        // Ensure description is never null
        $validated['description'] = $validated['description'] ?? '';



        // For non-admin users, don't allow setting status to draft or archive
        /** @var \App\Models\User $user */
        $user = Auth::user();
        if (!$user->isAdmin() && ($validated['status'] === 'draft' || $validated['status'] === 'archive')) {
            $validated['status'] = 'ongoing';
        }

        // Handle image upload
        if ($request->hasFile('image')) {
            // Delete old image if exists
            if ($program->image_path) {
                $this->imageService->deleteImage($program->image_path);
            }

            $validated['image_path'] = $this->imageService->processAndStoreImage(
                $request->file('image'),
                'programs'
            );
        }

        // Remove the image field as it's not in the database
        unset($validated['image']);

        try {
            $program->update($validated);
            return response()->json(['message' => 'Program updated successfully']);
        } catch (\Illuminate\Database\QueryException $e) {
            // Log the error for debugging
            Log::error('Database error when updating program: ' . $e->getMessage());

            // Check if it's a unique constraint violation for transaction_code
            if (
                str_contains($e->getMessage(), 'programs_transaction_code_active_unique') ||
                str_contains($e->getMessage(), 'duplicate key value violates unique constraint') ||
                str_contains($e->getMessage(), 'UNIQUE constraint failed')
            ) {
                return response()->json([
                    'errors' => [
                        'transaction_code' => ['The kode unik is not unique.']
                    ]
                ], 422);
            }

            // Re-throw the exception if it's not a constraint violation we can handle
            throw $e;
        } catch (\Exception $e) {
            // Log any other errors
            Log::error('Unexpected error when updating program: ' . $e->getMessage());
            throw $e;
        }
    }

    /**
     * Generate a unique transaction code for programs.
     */
    public function generateUniqueCode()
    {
        try {
            /** @var \App\Models\User $user */
            $user = Auth::user();
            if (!$user->isAdmin()) {
                abort(403, 'Unauthorized action.');
            }

            $code = Program::generateUniqueTransactionCode();

            Log::info('Generated unique transaction code', ['code' => $code]);

            return response()->json(['transaction_code' => $code]);
        } catch (\Exception $e) {
            Log::error('Error generating unique transaction code', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json(['error' => 'Failed to generate unique code'], 500);
        }
    }

    /**
     * Remove the specified program from storage.
     */
    public function destroy(Program $program)
    {
        /** @var \App\Models\User $user */
        $user = Auth::user();
        if (!$user->isAdmin()) {
            abort(403, 'Unauthorized action.');
        }

        // Delete the image if exists
        if ($program->image_path) {
            $this->imageService->deleteImage($program->image_path);
        }

        $program->delete();

        return response()->json(['message' => 'Program deleted successfully']);
    }

    /**
     * Search programs by name for autocomplete.
     */
    public function searchPrograms(Request $request)
    {
        $query = $request->get('q');

        if (empty($query) || strlen($query) < 2) {
            return response()->json([]);
        }

        $programs = Program::where('name', 'ilike', '%' . $query . '%')
            ->select('id', 'name', 'end_at', 'status')
            ->orderBy('name')
            ->limit(10)
            ->get();

        return response()->json($programs);
    }

    /**
     * Get a specific program by ID for the component.
     */
    public function getProgram($id)
    {
        $program = Program::select('id', 'name', 'end_at', 'status')
            ->findOrFail($id);

        return response()->json($program);
    }

    /**
     * Get multiple programs by IDs.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getMultiplePrograms(Request $request)
    {
        $ids = explode(',', $request->ids);
        $programs = \App\Models\Program::whereIn('id', $ids)->get();
        return response()->json($programs);
    }
}
